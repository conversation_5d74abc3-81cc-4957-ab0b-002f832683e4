#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快乐8服务 - FastAPI后端
集成爬虫、数据分析和数据库存储功能
"""

import pandas as pd
import pymysql
from datetime import datetime
from collections import Counter
from typing import List, Dict, Optional
from contextlib import asynccontextmanager

from fastapi import FastAPI
from fastapi.responses import FileResponse
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel

import requests
from bs4 import BeautifulSoup
import re

# 数据库配置
DB_CONFIG = {
    'host': '**************',
    'port': 8336,
    'user': 'root',
    'password': '123com123COM',
    'database': 'happy8',
    'charset': 'utf8mb4'
}

class Happy8Crawler:
    """快乐8爬虫类"""
    def __init__(self):
        self.base_url = "https://view.lottery.sina.com.cn/lotto/pc_zst/index"
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        self.session = requests.Session()
        self.session.headers.update(self.headers)
        
    def get_lottery_data(self, period_count: int = 500) -> List[Dict]:
        """获取快乐8开奖数据"""
        print(f"开始爬取最近{period_count}期快乐8开奖数据...")

        params = {
            'lottoType': 'kl8',
            'actionType': 'chzs',
            'type': str(period_count),
            'dpc': '1'
        }

        try:
            response = self.session.get(self.base_url, params=params, timeout=30)
            response.raise_for_status()
            response.encoding = 'utf-8'

            print("页面获取成功，开始解析数据...")
            lottery_data = self._parse_lottery_data(response.text)
            return lottery_data

        except requests.RequestException as e:
            print(f"请求失败: {e}")
            return []
    
    def _parse_lottery_data(self, html_content: str) -> List[Dict]:
        """解析HTML页面，提取开奖数据"""
        soup = BeautifulSoup(html_content, 'html.parser')
        lottery_data = []

        table = soup.find('table')
        if not table:
            print("未找到table标签，查找其他可能的容器...")
            containers = soup.find_all(['div', 'section'], class_=re.compile(r'table|data|result'))
            print(f"找到 {len(containers)} 个可能的数据容器")
            if containers:
                table = containers[0]
            else:
                return []

        print(f"找到表格，开始解析...")
        rows = table.find_all('tr')
        print(f"找到 {len(rows)} 行数据")

        for idx, row in enumerate(rows):
            cells = row.find_all(['td', 'th'])
            print(f"第{idx+1}行有 {len(cells)} 个单元格")

            if len(cells) < 10:
                continue

            period_cell = cells[0]
            period_text = period_cell.get_text(strip=True)
            print(f"期号文本: '{period_text}'")

            if not re.match(r'^\d{7}$', period_text):
                print(f"期号格式不匹配: {period_text}")
                continue

            winning_numbers = []
            for i in range(1, min(81, len(cells))):
                cell = cells[i]
                if self._is_winning_number(cell):
                    winning_numbers.append(f"{i:02d}")
                    print(f"找到中奖号码: {i:02d}")

            print(f"期号 {period_text} 找到 {len(winning_numbers)} 个中奖号码")

            if len(winning_numbers) >= 15:
                # 如果超过20个号码，保留最后20个
                if len(winning_numbers) > 20:
                    winning_numbers = winning_numbers[-20:]
                    
                lottery_data.append({
                    'period': period_text,
                    'numbers': winning_numbers,
                    'numbers_str': ' '.join(winning_numbers)
                })
                print(f"解析期号: {period_text}, 号码: {' '.join(winning_numbers)} (共{len(winning_numbers)}个)")

        print(f"成功解析 {len(lottery_data)} 期数据")
        return lottery_data
    
    def _is_winning_number(self, cell) -> bool:
        """判断某个号码位置是否为中奖号码"""
        cell_class = cell.get('class', [])
        if 'chartball01' in cell_class:
            return True
        return False

class WindowSizeAnalyzer:
    """窗口大小分析器"""
    
    def __init__(self, lottery_data: List[Dict]):
        self.lottery_data = lottery_data
        self.df = self._prepare_dataframe()
    
    def _prepare_dataframe(self) -> pd.DataFrame:
        """准备数据框"""
        df = pd.DataFrame(self.lottery_data)
        df['numbers_list'] = df['numbers'].apply(lambda x: [int(num) for num in x])
        return df
    
    def get_top_numbers(self, history_data):
        """获取历史数据中出现频次最高的数字"""
        all_numbers = []
        for numbers in history_data:
            all_numbers.extend(numbers)
        
        counter = Counter(all_numbers)
        
        if not counter:
            return [], False
        
        max_count = max(counter.values())
        top_numbers = [num for num, count in counter.items() if count == max_count]
        
        is_valid = len(top_numbers) == 2
        
        return sorted(top_numbers), is_valid

    def check_prediction(self, prediction_numbers, actual_numbers):
        """检查预测是否命中"""
        return any(num in actual_numbers for num in prediction_numbers)

    def backtest_single_window_size(self, window_size):
        """对单个window_size进行回测"""
        total_profit = 0
        bet_count = 0
        win_count = 0
        loss_count = 0
        bet_results = []

        for i in range(window_size, len(self.df)):
            history_data = self.df.iloc[i-window_size:i]['numbers_list'].tolist()
            current_numbers = self.df.iloc[i]['numbers_list']

            top_numbers, is_valid = self.get_top_numbers(history_data)

            if not is_valid:
                continue

            bet_count += 1
            prediction_numbers = top_numbers
            hit = self.check_prediction(prediction_numbers, current_numbers)

            if hit:
                profit_change = 19
                total_profit += profit_change
                win_count += 1
                bet_results.append(True)
            else:
                profit_change = -2
                total_profit += profit_change
                loss_count += 1
                bet_results.append(False)

        # 分析连续亏损
        loss_streaks = []
        current_streak = 0
        max_consecutive_losses = 0

        for hit in bet_results:
            if not hit:
                current_streak += 1
                max_consecutive_losses = max(max_consecutive_losses, current_streak)
            else:
                if current_streak > 0:
                    loss_streaks.append(current_streak)
                current_streak = 0

        if current_streak > 0:
            loss_streaks.append(current_streak)

        # 计算当前连续失败次数
        current_consecutive_failures = 0
        if bet_results:
            for i in range(len(bet_results) - 1, -1, -1):
                if not bet_results[i]:
                    current_consecutive_failures += 1
                else:
                    break

        hit_rate = (win_count / bet_count * 100) if bet_count > 0 else 0
        loss_streak_counts = dict(Counter(loss_streaks)) if loss_streaks else {}

        return {
            'window_size': window_size,
            'final_profit': total_profit,
            'bet_count': bet_count,
            'win_count': win_count,
            'loss_count': loss_count,
            'hit_rate': hit_rate,
            'max_consecutive_losses': max_consecutive_losses,
            'loss_streak_counts': loss_streak_counts,
            'current_consecutive_failures': current_consecutive_failures
        }

    def get_next_prediction(self, window_size):
        """基于最新数据预测下一期号码"""
        if len(self.df) < window_size:
            return ""

        history_data = self.df.tail(window_size)['numbers_list'].tolist()
        top_numbers, is_valid = self.get_top_numbers(history_data)

        if is_valid:
            return str(top_numbers)
        else:
            return ""

    def analyze_all_window_sizes(self):
        """分析所有window_size从2到151"""
        results = []
        
        print("开始分析不同的window_size...")
        for window_size in range(2, 152):
            result = self.backtest_single_window_size(window_size)
            
            # 添加预测
            next_prediction = self.get_next_prediction(window_size)
            result['next_prediction'] = next_prediction
            
            # 添加分析日期和最新期号
            result['analysis_date'] = datetime.now().strftime('%Y-%m-%d')
            result['latest_period'] = self.df.iloc[-1]['period'] if len(self.df) > 0 else "未知期号"
            result['loss_streak_counts'] = str(result['loss_streak_counts'])
            
            results.append(result)
            
            if window_size % 10 == 0:
                print(f"已完成 window_size {window_size} 的分析")

        return results

class DatabaseManager:
    """数据库管理器"""

    def __init__(self):
        self.config = DB_CONFIG

    def get_connection(self):
        """获取数据库连接"""
        return pymysql.connect(**self.config)

    def init_database(self):
        """初始化数据库表"""
        conn = self.get_connection()
        try:
            cursor = conn.cursor()

            # 创建分析结果表
            create_table_sql = """
            CREATE TABLE IF NOT EXISTS window_analysis_results (
                id INT AUTO_INCREMENT PRIMARY KEY,
                analysis_date DATE NOT NULL,
                latest_period VARCHAR(20) NOT NULL,
                window_size INT NOT NULL,
                final_profit DECIMAL(10,2) NOT NULL,
                bet_count INT NOT NULL,
                win_count INT NOT NULL,
                loss_count INT NOT NULL,
                hit_rate DECIMAL(5,2) NOT NULL,
                max_consecutive_losses INT NOT NULL,
                current_consecutive_failures INT NOT NULL,
                loss_streak_counts TEXT,
                next_prediction VARCHAR(100),
                bet_amount DECIMAL(10,2) DEFAULT 0 COMMENT '投注金额',
                bet_status ENUM('未开奖', '中', '未中') DEFAULT '未开奖' COMMENT '投注状态',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_analysis_date (analysis_date),
                INDEX idx_latest_period (latest_period),
                INDEX idx_window_size (window_size),
                UNIQUE KEY unique_analysis (analysis_date, latest_period, window_size)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """

            cursor.execute(create_table_sql)
            conn.commit()
            print("数据库表初始化成功")

        except Exception as e:
            print(f"数据库初始化失败: {e}")
            conn.rollback()
        finally:
            conn.close()

    def save_analysis_results(self, results: List[Dict]):
        """保存分析结果到数据库"""
        if not results:
            return False

        conn = self.get_connection()
        try:
            cursor = conn.cursor()

            # 先删除相同日期和期号的旧数据
            analysis_date = results[0]['analysis_date']
            latest_period = results[0]['latest_period']

            delete_sql = """
            DELETE FROM window_analysis_results
            WHERE analysis_date = %s AND latest_period = %s
            """
            cursor.execute(delete_sql, (analysis_date, latest_period))

            # 插入新数据
            insert_sql = """
            INSERT INTO window_analysis_results (
                analysis_date, latest_period, window_size, final_profit, bet_count,
                win_count, loss_count, hit_rate, max_consecutive_losses,
                current_consecutive_failures, loss_streak_counts, next_prediction,
                bet_amount, bet_status
            ) VALUES (
                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
            )
            """

            for result in results:
                cursor.execute(insert_sql, (
                    result['analysis_date'],
                    result['latest_period'],
                    result['window_size'],
                    result['final_profit'],
                    result['bet_count'],
                    result['win_count'],
                    result['loss_count'],
                    result['hit_rate'],
                    result['max_consecutive_losses'],
                    result['current_consecutive_failures'],
                    result['loss_streak_counts'],
                    result['next_prediction'],
                    0,  # bet_amount 默认为0
                    '未开奖'  # bet_status 默认为未开奖
                ))

            conn.commit()
            print(f"成功保存 {len(results)} 条分析结果到数据库")
            return True

        except Exception as e:
            print(f"保存数据失败: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()

    def get_latest_results(self, limit: int = 150) -> List[Dict]:
        """获取最新的分析结果"""
        conn = self.get_connection()
        try:
            cursor = conn.cursor(pymysql.cursors.DictCursor)

            # 获取最新一期的数据
            sql = """
            SELECT * FROM window_analysis_results
            WHERE (analysis_date, latest_period) = (
                SELECT analysis_date, latest_period
                FROM window_analysis_results
                ORDER BY analysis_date DESC, latest_period DESC
                LIMIT 1
            )
            ORDER BY window_size ASC
            LIMIT %s
            """

            cursor.execute(sql, (limit,))
            results = cursor.fetchall()

            # 转换数据类型
            for result in results:
                result['final_profit'] = float(result['final_profit'])
                result['hit_rate'] = float(result['hit_rate'])
                result['analysis_date'] = result['analysis_date'].strftime('%Y-%m-%d')
                result['bet_amount'] = float(result['bet_amount']) if result['bet_amount'] else 0

            return results

        except Exception as e:
            print(f"获取数据失败: {e}")
            return []
        finally:
            conn.close()

    def get_results_by_period(self, latest_period: str) -> List[Dict]:
        """根据期号获取分析结果"""
        conn = self.get_connection()
        try:
            cursor = conn.cursor(pymysql.cursors.DictCursor)

            sql = """
            SELECT * FROM window_analysis_results
            WHERE latest_period = %s
            ORDER BY window_size ASC
            """

            cursor.execute(sql, (latest_period,))
            results = cursor.fetchall()

            # 转换数据类型
            for result in results:
                result['final_profit'] = float(result['final_profit'])
                result['hit_rate'] = float(result['hit_rate'])
                result['analysis_date'] = result['analysis_date'].strftime('%Y-%m-%d')
                result['bet_amount'] = float(result['bet_amount']) if result['bet_amount'] else 0

            return results

        except Exception as e:
            print(f"获取数据失败: {e}")
            return []
        finally:
            conn.close()

    def get_available_periods(self) -> List[str]:
        """获取可用的期号列表"""
        conn = self.get_connection()
        try:
            cursor = conn.cursor()

            sql = """
            SELECT DISTINCT latest_period
            FROM window_analysis_results
            ORDER BY latest_period DESC
            """

            cursor.execute(sql)
            results = cursor.fetchall()

            return [row[0] for row in results]

        except Exception as e:
            print(f"获取期号列表失败: {e}")
            return []
        finally:
            conn.close()

    def update_bet_info(self, record_id: int, bet_amount: float, bet_status: str) -> bool:
        """更新投注信息"""
        conn = self.get_connection()
        try:
            cursor = conn.cursor()

            sql = """
            UPDATE window_analysis_results
            SET bet_amount = %s, bet_status = %s, updated_at = CURRENT_TIMESTAMP
            WHERE id = %s
            """

            cursor.execute(sql, (bet_amount, bet_status, record_id))
            conn.commit()

            return cursor.rowcount > 0

        except Exception as e:
            print(f"更新投注信息失败: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()

    def get_bet_statistics(self) -> Dict:
        """获取投注统计信息"""
        conn = self.get_connection()
        try:
            cursor = conn.cursor()

            # 总投入金额
            cursor.execute("SELECT SUM(bet_amount) FROM window_analysis_results WHERE bet_amount > 0")
            total_investment = cursor.fetchone()[0] or 0

            # 中奖金额 (假设中奖奖金是投注金额的19倍)
            cursor.execute("SELECT SUM(bet_amount * 19) FROM window_analysis_results WHERE bet_status = '中'")
            win_amount = cursor.fetchone()[0] or 0

            # 未开奖金额
            cursor.execute("SELECT SUM(bet_amount) FROM window_analysis_results WHERE bet_status = '未开奖'")
            pending_amount = cursor.fetchone()[0] or 0

            # 盈亏统计
            profit_loss = win_amount - total_investment

            return {
                'total_investment': float(total_investment),
                'win_amount': float(win_amount),
                'pending_amount': float(pending_amount),
                'profit_loss': float(profit_loss)
            }

        except Exception as e:
            print(f"获取投注统计失败: {e}")
            return {
                'total_investment': 0,
                'win_amount': 0,
                'pending_amount': 0,
                'profit_loss': 0
            }
        finally:
            conn.close()

# Pydantic模型
class AnalysisResponse(BaseModel):
    success: bool
    message: str
    data: Optional[List[Dict]] = None

class PeriodListResponse(BaseModel):
    success: bool
    periods: List[str]

class BetUpdateRequest(BaseModel):
    record_id: int
    bet_amount: float
    bet_status: str

class BetUpdateResponse(BaseModel):
    success: bool
    message: str

class BetStatsResponse(BaseModel):
    success: bool
    data: Dict

# 全局变量
db_manager = DatabaseManager()

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时初始化数据库
    print("初始化数据库...")
    db_manager.init_database()
    yield
    # 关闭时的清理工作
    print("应用关闭")

# 创建FastAPI应用
app = FastAPI(
    title="快乐8数据分析服务",
    description="快乐8数据爬取、分析和可视化服务",
    version="1.0.0",
    lifespan=lifespan
)

# 创建静态文件目录
app.mount("/static", StaticFiles(directory="static"), name="static")

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def read_root():
    """返回前端页面"""
    return FileResponse("happy8_frontend.html")

@app.get("/api/health")
async def health_check():
    """健康检查"""
    return {"status": "ok", "timestamp": datetime.now().isoformat()}

@app.post("/api/refresh-data", response_model=AnalysisResponse)
async def refresh_data():
    """获取最新数据并进行分析"""
    try:
        print("开始获取最新数据...")

        # 1. 爬取数据
        crawler = Happy8Crawler()
        lottery_data = crawler.get_lottery_data(500)

        if not lottery_data:
            return AnalysisResponse(
                success=False,
                message="爬取数据失败，请检查网络连接"
            )

        print(f"成功爬取 {len(lottery_data)} 期数据")

        # 2. 进行分析
        analyzer = WindowSizeAnalyzer(lottery_data)
        results = analyzer.analyze_all_window_sizes()

        print(f"完成分析，共 {len(results)} 个结果")

        # 3. 保存到数据库
        success = db_manager.save_analysis_results(results)

        if success:
            return AnalysisResponse(
                success=True,
                message=f"数据更新成功！爬取 {len(lottery_data)} 期数据，分析 {len(results)} 个窗口大小",
                data=results[:10]  # 返回前10个结果作为预览
            )
        else:
            return AnalysisResponse(
                success=False,
                message="数据分析完成，但保存到数据库失败"
            )

    except Exception as e:
        print(f"刷新数据失败: {e}")
        return AnalysisResponse(
            success=False,
            message=f"数据更新失败: {str(e)}"
        )

@app.get("/api/results", response_model=AnalysisResponse)
async def get_results(limit: int = 150):
    """获取最新的分析结果"""
    try:
        results = db_manager.get_latest_results(limit)

        if results:
            return AnalysisResponse(
                success=True,
                message=f"获取到 {len(results)} 条结果",
                data=results
            )
        else:
            return AnalysisResponse(
                success=False,
                message="暂无数据，请先点击'获取最新数据'按钮"
            )

    except Exception as e:
        print(f"获取结果失败: {e}")
        return AnalysisResponse(
            success=False,
            message=f"获取结果失败: {str(e)}"
        )

@app.get("/api/results/{period}", response_model=AnalysisResponse)
async def get_results_by_period(period: str):
    """根据期号获取分析结果"""
    try:
        results = db_manager.get_results_by_period(period)

        if results:
            return AnalysisResponse(
                success=True,
                message=f"获取到期号 {period} 的 {len(results)} 条结果",
                data=results
            )
        else:
            return AnalysisResponse(
                success=False,
                message=f"未找到期号 {period} 的数据"
            )

    except Exception as e:
        print(f"获取结果失败: {e}")
        return AnalysisResponse(
            success=False,
            message=f"获取结果失败: {str(e)}"
        )

@app.get("/api/periods", response_model=PeriodListResponse)
async def get_periods():
    """获取可用的期号列表"""
    try:
        periods = db_manager.get_available_periods()
        return PeriodListResponse(
            success=True,
            periods=periods
        )
    except Exception as e:
        print(f"获取期号列表失败: {e}")
        return PeriodListResponse(
            success=False,
            periods=[]
        )

@app.put("/api/bet-info", response_model=BetUpdateResponse)
async def update_bet_info(request: BetUpdateRequest):
    """更新投注信息"""
    try:
        # 验证投注状态
        if request.bet_status not in ['未开奖', '中', '未中']:
            return BetUpdateResponse(
                success=False,
                message="投注状态必须是：未开奖、中、未中"
            )

        # 验证投注金额
        if request.bet_amount < 0:
            return BetUpdateResponse(
                success=False,
                message="投注金额不能为负数"
            )

        success = db_manager.update_bet_info(
            request.record_id,
            request.bet_amount,
            request.bet_status
        )

        if success:
            return BetUpdateResponse(
                success=True,
                message="投注信息更新成功"
            )
        else:
            return BetUpdateResponse(
                success=False,
                message="投注信息更新失败，记录不存在"
            )

    except Exception as e:
        print(f"更新投注信息失败: {e}")
        return BetUpdateResponse(
            success=False,
            message=f"更新失败: {str(e)}"
        )

@app.get("/api/bet-stats", response_model=BetStatsResponse)
async def get_bet_statistics():
    """获取投注统计信息"""
    try:
        stats = db_manager.get_bet_statistics()
        return BetStatsResponse(
            success=True,
            data=stats
        )
    except Exception as e:
        print(f"获取投注统计失败: {e}")
        return BetStatsResponse(
            success=False,
            data={}
        )

if __name__ == "__main__":
    import uvicorn

    print("启动快乐8数据分析服务...")
    print("访问地址: http://localhost:8899")

    uvicorn.run(
        "happy8_service:app",
        host="0.0.0.0",
        port=8899,
        reload=True,
        log_level="info"
    )
